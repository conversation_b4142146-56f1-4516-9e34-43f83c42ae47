"use client";

import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { supabase } from "@/lib/supabase";
import {
  LogOut,
  Settings,
  BarChart3,
  Database,
  Users,
  Megaphone,
  Store,
  Phone,
  Image,
  MapPin,
} from "lucide-react";
import { VillageInfoManager } from "./VillageInfoManager";
import { AnnouncementManager } from "./AnnouncementManager";
import { UMKMManager } from "./UMKMManager";
import { ContactManager } from "./ContactManager";
import { StorageMonitor } from "./StorageMonitor";
import { GalleryManager } from "./GalleryManager";
import { TourismManager } from "./TourismManager";
import Link from "next/link";

interface AdminDashboardProps {
  user: any;
}

type ActiveSection =
  | "overview"
  | "village"
  | "announcements"
  | "umkm"
  | "contacts"
  | "gallery"
  | "tourism"
  | "storage";

interface DashboardStats {
  totalUMKM: number;
  totalAnnouncements: number;
  totalContacts: number;
  totalGallery: number;
  totalTourism: number;
  storageUsed: string;
}

export function AdminDashboard({ user }: AdminDashboardProps) {
  const [activeSection, setActiveSection] = useState<ActiveSection>("overview");
  const [stats, setStats] = useState<DashboardStats>({
    totalUMKM: 0,
    totalAnnouncements: 0,
    totalContacts: 0,
    totalGallery: 0,
    totalTourism: 0,
    storageUsed: "0 MB",
  });
  const [loading, setLoading] = useState(true);

  const fetchDashboardStats = async () => {
    try {
      setLoading(true);

      // Fetch UMKM count
      const { count: umkmCount } = await supabase
        .from("umkm")
        .select("*", { count: "exact", head: true });

      // Fetch announcements count
      const { count: announcementCount } = await supabase
        .from("announcements")
        .select("*", { count: "exact", head: true });

      // Fetch contacts count
      const { count: contactCount } = await supabase
        .from("contacts")
        .select("*", { count: "exact", head: true });

      // Fetch gallery count
      const { count: galleryCount } = await supabase
        .from("gallery")
        .select("*", { count: "exact", head: true });

      // Fetch tourism count
      const { count: tourismCount } = await supabase
        .from("tourism")
        .select("*", { count: "exact", head: true });

      // Get storage usage (simplified - you might want to implement actual storage calculation)
      const { data: storageData } = await supabase.storage
        .from("images")
        .list();

      let storageSize = 0;
      if (storageData) {
        storageSize = storageData.reduce(
          (total, file) => total + (file.metadata?.size || 0),
          0
        );
      }

      const storageMB = (storageSize / (1024 * 1024)).toFixed(2);

      setStats({
        totalUMKM: umkmCount || 0,
        totalAnnouncements: announcementCount || 0,
        totalContacts: contactCount || 0,
        totalGallery: galleryCount || 0,
        totalTourism: tourismCount || 0,
        storageUsed: `${storageMB} MB`,
      });
    } catch (error) {
      console.error("Error fetching dashboard stats:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchDashboardStats();
  }, []);

  const handleLogout = async () => {
    await supabase.auth.signOut();
    window.location.reload();
  };

  const menuItems = [
    { id: "overview", label: "Ringkasan", icon: BarChart3 },
    { id: "village", label: "Profil", icon: Settings },
    { id: "announcements", label: "Pengumuman", icon: Megaphone },
    { id: "umkm", label: "UMKM", icon: Store },
    { id: "contacts", label: "Kontak", icon: Phone },
    { id: "gallery", label: "Galeri", icon: Image },
    { id: "tourism", label: "Wisata", icon: MapPin },
    // { id: "storage", label: "Storage", icon: Database },
  ];

  const renderContent = () => {
    switch (activeSection) {
      case "village":
        return <VillageInfoManager />;
      case "announcements":
        return <AnnouncementManager />;
      case "umkm":
        return <UMKMManager />;
      case "contacts":
        return <ContactManager />;
      case "gallery":
        return <GalleryManager />;
      case "tourism":
        return <TourismManager />;
      case "storage":
        return <StorageMonitor />;
      default:
        return (
          <div className="space-y-6">
            {/* Summary Cards */}
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6">
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">
                        Total UMKM
                      </p>
                      <p className="text-2xl font-bold">
                        {loading ? "..." : stats.totalUMKM}
                      </p>
                    </div>
                    <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                      <Store className="w-6 h-6 text-blue-600" />
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">
                        Total Pengumuman
                      </p>
                      <p className="text-2xl font-bold">
                        {loading ? "..." : stats.totalAnnouncements}
                      </p>
                    </div>
                    <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                      <Megaphone className="w-6 h-6 text-green-600" />
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">
                        Total Kontak
                      </p>
                      <p className="text-2xl font-bold">
                        {loading ? "..." : stats.totalContacts}
                      </p>
                    </div>
                    <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                      <Phone className="w-6 h-6 text-purple-600" />
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">
                        Total Galeri
                      </p>
                      <p className="text-2xl font-bold">
                        {loading ? "..." : stats.totalGallery}
                      </p>
                    </div>
                    <div className="w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center">
                      <Image className="w-6 h-6 text-indigo-600" />
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">
                        Total Wisata
                      </p>
                      <p className="text-2xl font-bold">
                        {loading ? "..." : stats.totalTourism}
                      </p>
                    </div>
                    <div className="w-12 h-12 bg-teal-100 rounded-lg flex items-center justify-center">
                      <MapPin className="w-6 h-6 text-teal-600" />
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">
                        Storage Digunakan
                      </p>
                      <p className="text-2xl font-bold">
                        {loading ? "..." : stats.storageUsed}
                      </p>
                    </div>
                    <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                      <Database className="w-6 h-6 text-orange-600" />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Quick Actions */}
            <Card>
              <CardHeader>
                <CardTitle>Aksi Cepat</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-5">
                  <Button
                    onClick={() => setActiveSection("umkm")}
                    className="flex items-center space-x-2 h-12"
                    variant="outline"
                  >
                    <Store className="w-4 h-4" />
                    <span>Kelola UMKM</span>
                  </Button>
                  <Button
                    onClick={() => setActiveSection("announcements")}
                    className="flex items-center space-x-2 h-12"
                    variant="outline"
                  >
                    <Megaphone className="w-4 h-4" />
                    <span>Kelola Pengumuman</span>
                  </Button>
                  <Button
                    onClick={() => setActiveSection("contacts")}
                    className="flex items-center space-x-2 h-12"
                    variant="outline"
                  >
                    <Phone className="w-4 h-4" />
                    <span>Kelola Kontak</span>
                  </Button>
                  <Button
                    onClick={() => setActiveSection("tourism")}
                    className="flex items-center space-x-2 h-12"
                    variant="outline"
                  >
                    <MapPin className="w-4 h-4" />
                    <span>Kelola Wisata</span>
                  </Button>
                  <Button
                    onClick={() => fetchDashboardStats()}
                    className="flex items-center space-x-2 h-12"
                    variant="outline"
                    disabled={loading}
                  >
                    <BarChart3 className="w-4 h-4" />
                    <span>{loading ? "Memuat..." : "Refresh Data"}</span>
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        );
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 flex">
      {/* Sidebar */}
      <div className="w-64 bg-white shadow-lg">
        <div className="p-6 border-b">
          <h2 className="text-xl font-bold text-gray-800">Admin Panel</h2>
          <p className="text-sm text-gray-600">{user.email}</p>
        </div>

        <nav className="p-4">
          <ul className="space-y-2">
            {menuItems.map((item) => {
              const Icon = item.icon;
              return (
                <li key={item.id}>
                  <button
                    onClick={() => setActiveSection(item.id as ActiveSection)}
                    className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors ${
                      activeSection === item.id
                        ? "bg-green-100 text-green-700"
                        : "text-gray-700 hover:bg-gray-100"
                    }`}
                  >
                    <Icon className="w-5 h-5" />
                    <span>{item.label}</span>
                  </button>
                </li>
              );
            })}
          </ul>
        </nav>

        <div className="absolute bottom-4 left-4">
          <Button
            variant="outline"
            size="sm"
            onClick={handleLogout}
            className="flex items-center space-x-2"
          >
            <LogOut className="w-4 h-4" />
            <span>Logout</span>
          </Button>
          <Link href="/reset-password" className="flex items-center space-x-2">
            <LogOut className="w-4 h-4" />
            <span>Ganti Sandi</span>
          </Link>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 p-8">
        <div className="max-w-7xl mx-auto">
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900">
              {menuItems.find((item) => item.id === activeSection)?.label ||
                "Dashboard"}
            </h1>
          </div>

          {renderContent()}
        </div>
      </div>
    </div>
  );
}
