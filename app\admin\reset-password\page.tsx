"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { sendPasswordResetEmail, isValidEmail } from "@/lib/auth-utils";
import { Mail, ArrowLeft, CheckCircle } from "lucide-react";
import Link from "next/link";

export default function ResetPasswordPage() {
  const [email, setEmail] = useState("");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [success, setSuccess] = useState(false);

  const handleResetPassword = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError("");

    // Validate email format
    if (!isValidEmail(email)) {
      setError("Format email tidak valid");
      setLoading(false);
      return;
    }

    try {
      const result = await sendPasswordResetEmail(
        email,
        `${window.location.origin}/admin/reset-password/confirm`
      );

      if (!result.success) {
        setError(result.error || "Terjadi kesalahan saat mengirim email");
      } else {
        setSuccess(true);
      }
    } catch (error) {
      setError("Terjadi kesalahan saat mengirim email reset password");
    } finally {
      setLoading(false);
    }
  };

  if (success) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-50 flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <div className="w-16 h-16 bg-green-600 rounded-full flex items-center justify-center mx-auto mb-4">
              <CheckCircle className="w-8 h-8 text-white" />
            </div>
            <CardTitle className="text-2xl font-bold text-green-600">
              Email Terkirim!
            </CardTitle>
            <p className="text-gray-600">
              Kami telah mengirim link reset password ke email Anda
            </p>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <p className="text-sm text-green-800">
                <strong>Langkah selanjutnya:</strong>
              </p>
              <ol className="text-sm text-green-700 mt-2 space-y-1">
                <li>
                  1. Periksa email Anda di: <strong>{email}</strong>
                </li>
                <li>2. Klik link yang dikirimkan</li>
                <li>3. Masukkan password baru Anda</li>
              </ol>
            </div>

            <div className="text-center text-sm text-gray-600">
              <p>Tidak menerima email?</p>
              <button
                onClick={() => {
                  setSuccess(false);
                  setEmail("");
                }}
                className="text-green-600 hover:text-green-700 font-medium"
              >
                Kirim ulang
              </button>
            </div>

            <Link href="/admin">
              <Button variant="outline" className="w-full">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Kembali ke Login
              </Button>
            </Link>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-50 flex items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="w-16 h-16 bg-green-600 rounded-full flex items-center justify-center mx-auto mb-4">
            <Mail className="w-8 h-8 text-white" />
          </div>
          <CardTitle className="text-2xl font-bold">Reset Password</CardTitle>
          <p className="text-gray-600">
            Masukkan email admin untuk reset password
          </p>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleResetPassword} className="space-y-4">
            <div>
              <Label htmlFor="email">Email Admin</Label>
              <Input
                id="email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="<EMAIL>"
                required
                className="mt-1"
              />
              <p className="text-xs text-gray-500 mt-1">
                Masukkan email yang terdaftar sebagai admin
              </p>
            </div>

            {error && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                <p className="text-red-600 text-sm">{error}</p>
              </div>
            )}

            <Button
              type="submit"
              className="w-full bg-green-600 hover:bg-green-700"
              disabled={loading}
            >
              {loading ? (
                <div className="flex items-center space-x-2">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  <span>Mengirim...</span>
                </div>
              ) : (
                <div className="flex items-center space-x-2">
                  <Mail className="w-4 h-4" />
                  <span>Kirim Link Reset</span>
                </div>
              )}
            </Button>

            <div className="text-center">
              <Link
                href="/admin"
                className="text-sm text-gray-600 hover:text-green-600 transition-colors"
              >
                <ArrowLeft className="w-4 h-4 inline mr-1" />
                Kembali ke Login
              </Link>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
